# Dark Mode Toggle Integration Guide

## Overview
This guide shows you how to add a dark/light mode toggle to your Journal website with hover and shadow effects for all feature boxes.

## Files Created
1. `dark-mode-toggle.html` - Complete example implementation
2. `dark-mode-styles.css` - CSS styles for dark mode
3. `dark-mode-toggle.js` - JavaScript functionality

## Integration Steps

### Step 1: Update Your HTML Structure

Add this to your `<head>` section:

```html
<!-- Add dark mode CSS -->
<link rel="stylesheet" href="dark-mode-styles.css">

<!-- Configure Tailwind for dark mode (if using Tailwind) -->
<script>
    tailwind.config = {
        darkMode: 'class',
    }
</script>
```

Add this to your `<html>` tag:
```html
<html lang="en" class="light">
```

### Step 2: Add Toggle Button to Header

Add this toggle button to your header/navigation:

```html
<!-- Dark Mode Toggle -->
<div class="dark-mode-toggle">
    <span class="toggle-label">☀️</span>
    <button class="toggle-button" id="darkModeToggle" aria-label="Toggle dark mode">
        <div class="toggle-slider"></div>
    </button>
    <span class="toggle-label">🌙</span>
</div>
```

### Step 3: Update Feature Boxes

Add these classes to all your feature boxes:

```html
<!-- Example feature box with hover effects -->
<div class="feature-box bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700">
    <div class="text-blue-500 dark:text-blue-400 text-4xl mb-4">📚</div>
    <h4 class="text-xl font-semibold mb-3 text-gray-800 dark:text-white">Feature Title</h4>
    <p class="text-gray-600 dark:text-gray-300">Feature description...</p>
</div>
```

### Step 4: Add JavaScript

Add this before closing `</body>` tag:

```html
<!-- Dark Mode Toggle Script -->
<script src="dark-mode-toggle.js"></script>
```

## Key Classes for Dark Mode

### Background Colors
- `bg-white dark:bg-gray-900` - Main background
- `bg-gray-100 dark:bg-gray-800` - Secondary background
- `bg-white dark:bg-gray-800` - Card/box backgrounds

### Text Colors
- `text-gray-900 dark:text-white` - Primary text
- `text-gray-600 dark:text-gray-300` - Secondary text
- `text-gray-800 dark:text-white` - Headings

### Border Colors
- `border-gray-200 dark:border-gray-700` - Standard borders

### Hover Effects for Feature Boxes
The CSS includes these hover effects:
- `transform: translateY(-8px)` - Lift effect
- Enhanced shadows in both light and dark modes
- Smooth transitions

## Customization Options

### 1. Toggle Position
Modify the `.dark-mode-toggle` CSS to change position:
```css
.dark-mode-toggle {
    position: fixed;
    top: 20px;        /* Change this */
    right: 20px;      /* Change this */
}
```

### 2. Toggle Style
You can customize the toggle button appearance by modifying:
- `width` and `height` of `.toggle-button`
- `background-color` for different colors
- Add icons or text instead of emojis

### 3. Transition Speed
Change transition duration in the CSS:
```css
* {
    transition: background-color 0.3s ease, color 0.3s ease; /* Change 0.3s */
}
```

## Features Included

✅ **Smooth Transitions** - All elements transition smoothly between themes
✅ **Local Storage** - Remembers user preference
✅ **System Theme Detection** - Respects user's system preference
✅ **Keyboard Shortcut** - Ctrl/Cmd + Shift + D to toggle
✅ **Hover Effects** - Enhanced hover effects for all feature boxes
✅ **Responsive Design** - Works on all screen sizes
✅ **Accessibility** - Proper ARIA labels and keyboard support

## Browser Compatibility
- Modern browsers (Chrome 60+, Firefox 55+, Safari 12+)
- Uses CSS custom properties and modern JavaScript

## Testing
1. Open your website
2. Click the toggle button to switch between modes
3. Refresh the page to ensure preference is saved
4. Test hover effects on feature boxes
5. Try the keyboard shortcut (Ctrl/Cmd + Shift + D)

## Troubleshooting

### Toggle not appearing
- Ensure the JavaScript file is loaded
- Check browser console for errors
- Verify the HTML structure is correct

### Styles not applying
- Ensure CSS file is linked correctly
- Check that Tailwind CSS is configured for class-based dark mode
- Verify HTML elements have the correct classes

### Hover effects not working
- Ensure `.feature-box` class is applied to all feature containers
- Check that CSS transitions are not being overridden
- Verify the CSS file is loaded after other stylesheets
