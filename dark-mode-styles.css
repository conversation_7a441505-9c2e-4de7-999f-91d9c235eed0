/* Dark Mode Toggle Styles */

/* Smooth transitions for all elements */
* {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
}

/* Toggle Button Styles */
.dark-mode-toggle {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    display: flex;
    align-items: center;
    gap: 10px;
    background: rgba(255, 255, 255, 0.9);
    padding: 10px 15px;
    border-radius: 25px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

.dark .dark-mode-toggle {
    background: rgba(31, 41, 55, 0.9);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.toggle-button {
    position: relative;
    width: 60px;
    height: 30px;
    background-color: #e5e7eb;
    border-radius: 15px;
    cursor: pointer;
    transition: background-color 0.3s ease;
    border: none;
    outline: none;
}

.toggle-button.dark {
    background-color: #374151;
}

.toggle-slider {
    position: absolute;
    top: 3px;
    left: 3px;
    width: 24px;
    height: 24px;
    background-color: white;
    border-radius: 50%;
    transition: transform 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
}

.toggle-button.dark .toggle-slider {
    transform: translateX(30px);
}

.toggle-slider::before {
    content: '☀️';
}

.toggle-button.dark .toggle-slider::before {
    content: '🌙';
}

/* Feature Box Hover Effects */
.feature-box {
    transition: all 0.3s ease;
    cursor: pointer;
}

.feature-box:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.dark .feature-box:hover {
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.4);
}

/* Dark Mode Color Scheme */
:root {
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --border-color: #e5e7eb;
    --shadow-color: rgba(0, 0, 0, 0.1);
}

.dark {
    --bg-primary: #111827;
    --bg-secondary: #1f2937;
    --bg-tertiary: #374151;
    --text-primary: #f9fafb;
    --text-secondary: #d1d5db;
    --border-color: #4b5563;
    --shadow-color: rgba(0, 0, 0, 0.3);
}

/* Apply CSS variables */
body {
    background-color: var(--bg-primary);
    color: var(--text-primary);
}

.card, .feature-box, .discipline-box {
    background-color: var(--bg-secondary);
    border-color: var(--border-color);
    box-shadow: 0 4px 6px var(--shadow-color);
}

.card:hover, .feature-box:hover, .discipline-box:hover {
    box-shadow: 0 15px 35px var(--shadow-color);
}

/* Header and Navigation */
header {
    background-color: var(--bg-primary);
    border-bottom: 1px solid var(--border-color);
}

nav a {
    color: var(--text-secondary);
}

nav a:hover {
    color: var(--text-primary);
}

/* Buttons */
.btn-primary {
    background-color: #3b82f6;
    color: white;
}

.btn-primary:hover {
    background-color: #2563eb;
}

.dark .btn-primary {
    background-color: #2563eb;
}

.dark .btn-primary:hover {
    background-color: #1d4ed8;
}

/* Form Elements */
input, textarea, select {
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    border-color: var(--border-color);
}

input:focus, textarea:focus, select:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Footer */
footer {
    background-color: var(--bg-tertiary);
    color: var(--text-secondary);
}

/* Responsive Design for Toggle */
@media (max-width: 768px) {
    .dark-mode-toggle {
        top: 10px;
        right: 10px;
        padding: 8px 12px;
    }
    
    .toggle-button {
        width: 50px;
        height: 25px;
    }
    
    .toggle-slider {
        width: 19px;
        height: 19px;
        top: 3px;
        left: 3px;
    }
    
    .toggle-button.dark .toggle-slider {
        transform: translateX(25px);
    }
}
