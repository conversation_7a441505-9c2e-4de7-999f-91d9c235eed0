// Dark Mode Toggle Functionality
class DarkModeToggle {
    constructor() {
        this.init();
    }

    init() {
        // Create toggle button if it doesn't exist
        this.createToggleButton();
        
        // Set initial theme
        this.setInitialTheme();
        
        // Add event listeners
        this.addEventListeners();
    }

    createToggleButton() {
        // Check if toggle already exists
        if (document.getElementById('darkModeToggle')) {
            return;
        }

        // Create toggle container
        const toggleContainer = document.createElement('div');
        toggleContainer.className = 'dark-mode-toggle';
        toggleContainer.innerHTML = `
            <span class="toggle-label">☀️</span>
            <button class="toggle-button" id="darkModeToggle" aria-label="Toggle dark mode">
                <div class="toggle-slider"></div>
            </button>
            <span class="toggle-label">🌙</span>
        `;

        // Add to body
        document.body.appendChild(toggleContainer);
    }

    setInitialTheme() {
        const htmlElement = document.documentElement;
        const toggleButton = document.getElementById('darkModeToggle');
        
        // Check for saved theme preference
        const savedTheme = localStorage.getItem('theme');
        
        // Check system preference
        const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
        
        // Determine initial theme
        let initialTheme = savedTheme || (systemPrefersDark ? 'dark' : 'light');
        
        // Apply initial theme
        this.applyTheme(initialTheme);
    }

    applyTheme(theme) {
        const htmlElement = document.documentElement;
        const toggleButton = document.getElementById('darkModeToggle');
        
        if (theme === 'dark') {
            htmlElement.classList.add('dark');
            htmlElement.classList.remove('light');
            if (toggleButton) {
                toggleButton.classList.add('dark');
            }
        } else {
            htmlElement.classList.remove('dark');
            htmlElement.classList.add('light');
            if (toggleButton) {
                toggleButton.classList.remove('dark');
            }
        }
        
        // Save preference
        localStorage.setItem('theme', theme);
        
        // Dispatch custom event for other components
        window.dispatchEvent(new CustomEvent('themeChanged', { detail: { theme } }));
    }

    toggleTheme() {
        const htmlElement = document.documentElement;
        const currentTheme = htmlElement.classList.contains('dark') ? 'dark' : 'light';
        const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
        
        this.applyTheme(newTheme);
    }

    addEventListeners() {
        const toggleButton = document.getElementById('darkModeToggle');
        
        if (toggleButton) {
            toggleButton.addEventListener('click', () => this.toggleTheme());
        }

        // Listen for system theme changes
        window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
            // Only auto-switch if user hasn't manually set a preference
            if (!localStorage.getItem('theme')) {
                this.applyTheme(e.matches ? 'dark' : 'light');
            }
        });

        // Keyboard shortcut (Ctrl/Cmd + Shift + D)
        document.addEventListener('keydown', (e) => {
            if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'D') {
                e.preventDefault();
                this.toggleTheme();
            }
        });
    }

    // Public method to get current theme
    getCurrentTheme() {
        return document.documentElement.classList.contains('dark') ? 'dark' : 'light';
    }

    // Public method to set theme programmatically
    setTheme(theme) {
        if (theme === 'dark' || theme === 'light') {
            this.applyTheme(theme);
        }
    }
}

// Initialize dark mode toggle when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.darkModeToggle = new DarkModeToggle();
});

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DarkModeToggle;
}
