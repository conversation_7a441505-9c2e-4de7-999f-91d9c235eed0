<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Multidisciplinary Journal</title>
    <!-- Use the Lato font for a professional look -->
    <link href="https://fonts.googleapis.com/css2?family=Lato:wght@400;500;600;700&display=swap" rel="stylesheet">
    <!-- Include Tailwind CSS via CDN for styling -->
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            font-family: 'Lato', sans-serif;
            background-color: #000000;
        }
        /* A simple scroll-to-top button */
        #scrollToTopBtn {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            display: none;
            z-index: 1000;
        }
        /* Custom styles for the mobile menu transition */
        .mobile-menu {
            transition: transform 0.3s ease-in-out;
            transform: translateX(100%);
        }
        .mobile-menu.open {
            transform: translateX(0);
        }
        /* Custom scrollbar for better aesthetics */
        ::-webkit-scrollbar {
            width: 8px;
        }
        ::-webkit-scrollbar-track {
            background: #1a1a1a;
        }
        ::-webkit-scrollbar-thumb {
            background-color: #4b5563;
            border-radius: 4px;
        }
        /* Filter button styles */
        .filter-btn {
            background-color: rgba(255, 255, 255, 0.1);
            color: #d1d5db;
        }
        .active-filter-btn {
            background-color: #ffffff;
            color: #000000;
            box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
        }

        /* Inverted, rounded cursor */
        body {
            cursor: none;
        }

        #custom-cursor {
            pointer-events: none;
            position: fixed;
            top: 0;
            left: 0;
            z-index: 9999;
            width: 30px;
            height: 30px;
            background-color: transparent;
            border-radius: 50%;
            mix-blend-mode: difference;
            transition: transform 0.1s ease-out;
            transform: translate(-50%, -50%);
        }

        #custom-cursor::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 15px;
            height: 15px;
            background-color: white;
            border-radius: 50%;
        }

        /* Hero Section Cursor Animation */
        .cursor-animation {
            position: absolute;
            width: 20px;
            height: 20px;
            background-color: #000000;
            border-radius: 50%;
            opacity: 0.6;
            pointer-events: none;
        }

        .cursor-1 {
            top: 20%;
            left: 10%;
            animation: float1 8s ease-in-out infinite;
        }

        .cursor-2 {
            top: 60%;
            left: 80%;
            animation: float2 10s ease-in-out infinite;
        }

        .cursor-3 {
            top: 30%;
            left: 70%;
            animation: float3 12s ease-in-out infinite;
        }

        .cursor-4 {
            top: 80%;
            left: 20%;
            animation: float4 9s ease-in-out infinite;
        }

        .cursor-5 {
            top: 15%;
            left: 50%;
            animation: float5 11s ease-in-out infinite;
        }

        .cursor-6 {
            top: 70%;
            left: 40%;
            animation: float6 7s ease-in-out infinite;
        }

        @keyframes float1 {
            0%, 100% { transform: translate(0, 0) scale(1); }
            25% { transform: translate(30px, -20px) scale(1.2); }
            50% { transform: translate(-20px, 40px) scale(0.8); }
            75% { transform: translate(40px, 20px) scale(1.1); }
        }

        @keyframes float2 {
            0%, 100% { transform: translate(0, 0) scale(1); }
            33% { transform: translate(-40px, -30px) scale(1.3); }
            66% { transform: translate(20px, -50px) scale(0.9); }
        }

        @keyframes float3 {
            0%, 100% { transform: translate(0, 0) scale(1); }
            20% { transform: translate(-30px, 25px) scale(1.1); }
            40% { transform: translate(50px, -15px) scale(0.7); }
            60% { transform: translate(-25px, -40px) scale(1.4); }
            80% { transform: translate(35px, 30px) scale(0.9); }
        }

        @keyframes float4 {
            0%, 100% { transform: translate(0, 0) scale(1); }
            50% { transform: translate(-60px, -35px) scale(1.2); }
        }

        @keyframes float5 {
            0%, 100% { transform: translate(0, 0) scale(1); }
            25% { transform: translate(45px, 20px) scale(0.8); }
            50% { transform: translate(-35px, -25px) scale(1.3); }
            75% { transform: translate(25px, 45px) scale(1.1); }
        }

        @keyframes float6 {
            0%, 100% { transform: translate(0, 0) scale(1); }
            30% { transform: translate(-50px, 15px) scale(1.2); }
            70% { transform: translate(40px, -30px) scale(0.9); }
        }

        /* Responsive adjustments for cursor animation */
        @media (max-width: 768px) {
            .cursor-animation {
                width: 15px;
                height: 15px;
                opacity: 0.4;
            }
        }

        /* 3D Book Model Styles */
        .book-container {
            perspective: 1000px;
            perspective-origin: center center;
        }

        .book {
            position: relative;
            width: 300px;
            height: 400px;
            transform-style: preserve-3d;
            animation: bookFloat 6s ease-in-out infinite;
            margin: 0 auto;
        }

        .book-cover {
            position: absolute;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #2a2a2a 0%, #1a1a1a 50%, #0a0a0a 100%);
            border: 2px solid #333;
            border-radius: 8px;
            transform: rotateY(0deg) translateZ(15px);
            box-shadow:
                0 0 20px rgba(0, 0, 0, 0.8),
                inset 0 0 20px rgba(255, 255, 255, 0.1);
        }

        .book-spine {
            position: absolute;
            width: 30px;
            height: 100%;
            background: linear-gradient(90deg, #1a1a1a 0%, #2a2a2a 50%, #1a1a1a 100%);
            border: 2px solid #333;
            border-radius: 0 8px 8px 0;
            transform: rotateY(90deg) translateZ(15px);
            left: 100%;
            transform-origin: left center;
        }

        .book-back {
            position: absolute;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #1a1a1a 0%, #0a0a0a 50%, #2a2a2a 100%);
            border: 2px solid #333;
            border-radius: 8px;
            transform: rotateY(180deg) translateZ(15px);
        }

        .book-content {
            padding: 40px 30px;
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            position: relative;
        }

        .feather-icon {
            margin-bottom: 30px;
            opacity: 0.8;
            animation: featherGlow 3s ease-in-out infinite alternate;
        }

        .book-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            font-family: 'Lato', serif;
            letter-spacing: 2px;
        }

        .book-subtitle {
            font-size: 1.8rem;
            font-weight: 600;
            color: #444;
            margin-bottom: 8px;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            font-family: 'Lato', serif;
            letter-spacing: 1px;
        }

        .book-journal {
            font-size: 1.6rem;
            font-weight: 600;
            color: #444;
            margin-bottom: 15px;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            font-family: 'Lato', serif;
            letter-spacing: 1px;
        }

        .book-oij {
            font-size: 1.2rem;
            font-weight: 500;
            color: #555;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            font-family: 'Lato', serif;
            letter-spacing: 1px;
        }

        @keyframes bookFloat {
            0%, 100% {
                transform: rotateY(-15deg) rotateX(5deg) translateY(0px);
            }
            50% {
                transform: rotateY(-15deg) rotateX(5deg) translateY(-20px);
            }
        }

        @keyframes featherGlow {
            0% { opacity: 0.6; transform: scale(1); }
            100% { opacity: 1; transform: scale(1.05); }
        }

        /* Responsive adjustments for book */
        @media (max-width: 768px) {
            .book {
                width: 200px;
                height: 280px;
            }

            .book-content {
                padding: 25px 20px;
            }

            .feather-icon svg {
                width: 50px;
                height: 50px;
            }

            .book-title {
                font-size: 1.8rem;
            }

            .book-subtitle, .book-journal {
                font-size: 1.3rem;
            }

            .book-oij {
                font-size: 1rem;
            }
        }

        @media (max-width: 480px) {
            .book {
                width: 160px;
                height: 220px;
            }

            .book-content {
                padding: 20px 15px;
            }

            .feather-icon svg {
                width: 40px;
                height: 40px;
            }

            .book-title {
                font-size: 1.4rem;
            }

            .book-subtitle, .book-journal {
                font-size: 1.1rem;
            }

            .book-oij {
                font-size: 0.9rem;
            }
        }
        /* Enhanced 3D Book overrides and additions */
        .book-container { pointer-events: none; position: relative; }
        .book { width: 360px; height: 480px; animation: bookFloat 9s ease-in-out infinite; will-change: transform; }
        .book, .book-cover, .book-back, .book-spine, .pages, .page { backface-visibility: hidden; }



        /* Covers and spine refinements */
        .book-cover {
            transform-origin: left center;
            transform: rotateY(var(--cover-angle, 0deg)) translateZ(18px);
            background: #161616; /* solid matte */
            box-shadow: 0 16px 34px rgba(0,0,0,0.6), inset 0 0 0 1px rgba(255,255,255,0.03);
        }
        .book-back {
            background: #0f0f0f; /* solid matte */
            transform: rotateY(180deg) translateZ(18px);
            box-shadow: 0 14px 28px rgba(0,0,0,0.7), inset 0 0 0 1px rgba(255,255,255,0.03);
        }
        .book-spine { width: 34px; background: #141414; box-shadow: inset -4px 0 8px rgba(0,0,0,0.4); }

        /* Pages stack */
        .pages { position: absolute; left: 0; top: 0; width: 100%; height: 100%; transform: translateZ(17px); transform-style: preserve-3d; }
        .page { position: absolute; top: 4%; bottom: 4%; left: 2.2%; right: 8%; border-radius: 6px; background:
            linear-gradient(180deg, rgba(255,255,255,0.05), rgba(0,0,0,0.06));
            box-shadow: 0 2px 8px rgba(0,0,0,0.35), inset 0 0 0 1px rgba(255,255,255,0.04);
            transform-origin: left center; will-change: transform; }
        /* Stacked offset and depth */
        .page-1 { transform: rotateY(calc(var(--page1, -2deg))) translateZ(1px); }
        .page-2 { transform: rotateY(calc(var(--page2, -4deg))) translateZ(2px); }
        .page-3 { transform: rotateY(calc(var(--page3, -6deg))) translateZ(3px); }
        .page-4 { transform: rotateY(calc(var(--page4, -8deg))) translateZ(4px); }
        .page-5 { transform: rotateY(calc(var(--page5, -10deg))) translateZ(5px); }
        .page-6 { transform: rotateY(calc(var(--page6, -12deg))) translateZ(6px); }

        /* Embossed typography treatment */
        .book-title, .book-subtitle, .book-journal, .book-oij {
            color: #242424; letter-spacing: 1.5px;
            text-shadow: 1px 1px 0 #0a0a0a; /* subtle emboss only */
        }

        /* Enhanced floating with multi-axis rotation */
        @keyframes bookFloat {
            0%, 100% { transform: rotateY(-18deg) rotateX(8deg) rotateZ(-2deg) translateY(0); }
            50% { transform: rotateY(-18deg) rotateX(9deg) rotateZ(1.5deg) translateY(-16px); }
        }

        /* Responsive sizing for the enhanced book */
        @media (max-width: 1024px) { .book { width: 320px; height: 440px; } }
        @media (max-width: 768px)  { .book { width: 240px; height: 320px; } .book-lights .light{ width:100px;height:100px; } }
        @media (max-width: 480px)  { .book { width: 200px; height: 260px; } }

        /* Reduced motion accessibility */
        @media (prefers-reduced-motion: reduce) {
            .book { animation: none; }
        }
    </style>
</head>
<body class="bg-black text-gray-200">
    <!-- Custom Cursor -->
    <div id="custom-cursor"></div>

    <!-- Scroll-to-Top Button -->
    <button id="scrollToTopBtn" class="p-3 bg-white text-black rounded-full shadow-lg hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-white-500 focus:ring-opacity-50">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
        </svg>
    </button>

    <!-- Header & Navigation Bar -->
    <header class="sticky top-0 z-50 bg-black shadow-lg">
        <nav class="container mx-auto px-4 sm:px-6 lg:px-8 py-4 flex justify-between items-center">
            <!-- Logo Section -->
            <a href="#" class="flex items-center space-x-2">
                <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="70" height="70" viewBox="0 0 1024 1024"><path fill="#FEFEFE" d="M789.869 180.455C789.846 180.631 789.818 180.806 789.8 180.983C789.105 187.696 792.082 197.567 793.002 204.525C794.264 214.085 794.985 223.912 795.165 233.553C795.487 250.909 793.436 268.64 790.354 285.692C780.929 337.848 760.157 376.015 719.524 410.247C706.625 421.114 690.527 429.621 674.905 435.995C671.78 437.27 656.626 442.235 655.319 443.284C657.23 443.174 658.957 442.543 660.818 442.411C664.697 442.136 668.502 442.425 672.415 441.978C693.599 439.558 713.996 431.806 732.465 421.344C737.44 418.526 743.087 415.649 747.232 411.702L747.819 412.028C745.718 418.952 741.606 425.973 738.365 432.441C715.09 478.877 674.334 523.638 628.276 548.298C599.468 563.723 568.775 572.117 536.291 574.985C532.677 575.304 511.503 575.649 511.052 575.83C515.15 577.765 520.648 577.909 525.102 578.68C556.927 584.271 589.658 581.494 620.086 570.62C623.968 569.227 627.314 567.49 630.963 565.634L631.458 566.064C629.464 571.198 623.659 575.493 620.427 580.043C616.471 586.628 609.007 592.678 603.475 598.066C569.189 631.583 526.464 655.187 479.839 666.37C468.786 669.119 457.562 671.128 446.242 672.385C438.41 673.25 427.722 673.203 420.38 675.526C423.178 676.476 426.503 676.517 429.442 676.869C446.609 678.972 463.94 679.41 481.191 678.176C487.006 677.72 492.785 676.03 498.623 676.219C478.996 687.157 454.077 696.068 432.498 702.571C412.922 708.47 371.362 716.067 356.369 727.689C349.058 733.357 343.135 742.074 338.334 749.871C326.135 769.682 316.229 796.297 302.082 813.754C297.694 819.17 292.401 824.006 287.197 828.637C281.96 833.299 275.696 838.502 269.687 842.107L269.257 841.828C269.668 838.108 271.99 833.884 273.327 830.34C282.427 806.216 293.43 782.805 304.516 759.541C332.492 700.832 365.808 649.387 404.406 597.244C435.382 555.397 467.622 515.063 504.217 477.93C518.152 463.992 532.524 450.498 547.312 437.468C590.434 398.908 637.22 364.647 680.459 326.167C676.967 326.267 664.625 334.712 660.578 337.118C627.958 356.512 597.361 379.601 567.676 403.184C530.223 432.938 493.367 464.5 460.503 499.312C418.671 543.505 381.641 592.009 350.036 644.01C342.339 657.064 334.996 670.323 328.014 683.773C323.606 692.319 319.649 702.057 314.103 709.906C292.951 631.807 304.363 548.469 345.732 478.931C350.241 471.201 355.332 464.045 360.769 456.949C361.782 455.188 362.747 453.477 364.059 451.911C358.569 472.468 356.692 496.053 358.631 517.216C359.299 524.514 361.03 531.497 362.174 538.7C362.436 540.347 362.364 542.003 362.635 543.655L363.147 543.888C364.391 536.96 364.335 529.551 364.987 522.522C365.813 513.627 367.521 504.815 369.531 496.116C381.227 445.512 407.855 399.888 444.364 363.168C454.53 352.943 465.267 343.195 477.122 334.945C489.78 326.137 505.927 318.922 517.204 309.863C508.245 329.314 502.436 352.467 499.779 373.734L500.135 374.048C501.349 371.651 502.184 369.11 503.39 366.72C509.195 355.208 515.995 343.005 523.668 332.639C530.417 323.52 538.709 315.101 547.076 307.464C587.143 270.887 629.573 258.451 678.763 239.89C699.725 231.98 720.198 223.45 740.201 213.341C756.963 204.87 777.147 194.545 789.869 180.455Z"/><path fill="#FEFEFE" d="M314.394 708.325C352.513 629.903 402.729 557.964 463.2 495.144C487.348 470.509 512.763 447.15 539.344 425.161C572.171 397.309 606.513 371.294 642.214 347.236C657.245 337.242 673.443 329.014 688.776 319.506C687.24 322.046 682.953 324.44 680.459 326.167C676.967 326.267 664.625 334.712 660.578 337.118C627.958 356.512 597.361 379.601 567.676 403.184C530.223 432.938 493.367 464.5 460.503 499.312C418.671 543.505 381.641 592.009 350.036 644.01C342.339 657.064 334.996 670.323 328.014 683.773C323.606 692.319 319.649 702.057 314.103 709.906C292.951 631.807 304.363 548.469 345.732 478.931C350.241 471.201 355.332 464.045 360.769 456.949C359.752 460.039 357.424 462.831 355.585 465.487C318.782 518.647 300.537 587.534 305.31 651.941C306.726 671.056 310.976 689.533 314.394 708.325Z"/><path fill="#FEFEFE" d="M420.38 675.526C419.023 675.579 417.587 675.705 416.255 675.402C415.475 675.224 414.835 675.19 414.383 674.486C417.015 674.156 419.61 674.345 422.236 674.083C432.668 673.041 443.232 672.111 453.596 670.544C506.128 662.602 551.757 641.964 592.139 607.377C602.137 598.813 610.67 588.816 620.427 580.043C616.471 586.628 609.007 592.678 603.475 598.066C569.189 631.583 526.464 655.187 479.839 666.37C468.786 669.119 457.562 671.128 446.242 672.385C438.41 673.25 427.722 673.203 420.38 675.526Z"/></svg>
                <span class="text-xl sm:text-2xl font-bold text-gray-100">
                    Omni International Journal (OIJ)
                    <span class="block text-sm italic text-gray-400 font-normal">Publishing excellence across all disciplines.</span>
                </span>
            </a>

            <!-- Desktop Navigation Links -->
            <div class="hidden md:flex space-x-8">
                <a href="#about" class="text-gray-400 hover:text-white transition-colors duration-200 font-medium">About Us</a>
                <a href="#disciplines" class="text-gray-400 hover:text-white transition-colors duration-200 font-medium">Disciplines</a>
                <a href="#editorial-board" class="text-gray-400 hover:text-white transition-colors duration-200 font-medium">Editorial Board</a>
                <a href="#publications" class="text-gray-400 hover:text-white transition-colors duration-200 font-medium">Latest Publications</a>
                <a href="#archives" class="text-gray-400 hover:text-white transition-colors duration-200 font-medium">Archives</a>
                <a href="#contact" class="text-gray-400 hover:text-white transition-colors duration-200 font-medium">Contact</a>
            </div>

            <!-- Call to Action Button -->
            <div class="hidden md:block">
                <a href="#contact" class="inline-block bg-white text-black font-semibold py-2 px-6 rounded-full shadow-md hover:bg-gray-200 transition-colors duration-200">
                    Submit Manuscript
                </a>
            </div>

            <!-- Mobile Menu Button (Hamburger) -->
            <div class="md:hidden">
                <button id="mobile-menu-btn" class="text-gray-400 hover:text-white focus:outline-none">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16m-7 6h7" />
                    </svg>
                </button>
            </div>
        </nav>

        <!-- Mobile Navigation Menu (Hidden by default) -->
        <div id="mobile-menu" class="mobile-menu fixed top-0 right-0 h-full w-64 bg-black shadow-xl z-50 p-6 flex flex-col items-start space-y-6 md:hidden">
            <button id="close-mobile-menu-btn" class="self-end text-gray-400 hover:text-white focus:outline-none mb-4">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
            </button>
            <a href="#about" class="text-lg font-medium text-gray-200 hover:text-white w-full text-left">About Us</a>
            <a href="#disciplines" class="text-lg font-medium text-gray-200 hover:text-white w-full text-left">Disciplines</a>
            <a href="#editorial-board" class="text-lg font-medium text-gray-200 hover:text-white w-full text-left">Editorial Board</a>
            <a href="#publications" class="text-lg font-medium text-gray-200 hover:text-white w-full text-left">Latest Publications</a>
            <a href="#archives" class="text-lg font-medium text-gray-200 hover:text-white w-full text-left">Archives</a>
            <a href="#contact" class="text-lg font-medium text-gray-200 hover:text-white w-full text-left">Contact</a>
            <a href="#contact" class="inline-block bg-white text-black font-semibold py-2 px-6 rounded-full shadow-md hover:bg-gray-200 mt-4 w-full text-center">
                Submit Manuscript
            </a>
        </div>
    </header>

    <main>
        <!-- Hero Section -->
        <section id="hero" class="relative bg-black text-white h-screen flex items-center justify-center text-center overflow-hidden">
            <!-- Background overlay for text readability -->
            <div class="absolute inset-0 bg-black opacity-50 z-10"></div>

            <!-- Animated Cursor Background -->
            <div class="absolute inset-0 z-5">
                <div class="cursor-animation cursor-1"></div>
                <div class="cursor-animation cursor-2"></div>
                <div class="cursor-animation cursor-3"></div>
                <div class="cursor-animation cursor-4"></div>
                <div class="cursor-animation cursor-5"></div>
                <div class="cursor-animation cursor-6"></div>
            </div>

            <!-- 3D Book Model -->
            <div class="absolute inset-0 z-15 flex items-center justify-center" aria-hidden="true">
                <div class="book-container">
                    <!-- Book Scene -->
                    <div class="book" role="img" aria-label="Decorative 3D book">
                        <div class="book-back"></div>

                        <!-- Inner Pages -->
                        <div class="pages" aria-hidden="true">
                            <div class="page page-1"></div>
                            <div class="page page-2"></div>
                            <div class="page page-3"></div>
                            <div class="page page-4"></div>
                            <div class="page page-5"></div>
                            <div class="page page-6"></div>
                        </div>

                        <!-- Front Cover with logo/text -->
                        <div class="book-cover">
                            <div class="book-content">
                                <div class="feather-icon" aria-hidden="true">
                                    <svg width="84" height="84" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M20 80 C20 80, 25 75, 35 70 C45 65, 55 60, 65 50 C75 40, 80 30, 85 20 C87 15, 88 10, 85 8 C82 6, 78 8, 75 12 C70 18, 65 25, 60 32 C55 39, 50 45, 45 50 C40 55, 35 60, 30 65 C25 70, 22 75, 20 80 Z" fill="#303030" stroke="#1f1f1f" stroke-width="1"/>
                                        <path d="M35 70 C40 68, 45 65, 50 62" stroke="#4a4a4a" stroke-width="1" fill="none"/>
                                        <path d="M45 60 C50 58, 55 55, 60 52" stroke="#4a4a4a" stroke-width="1" fill="none"/>
                                        <path d="M55 50 C60 48, 65 45, 70 42" stroke="#4a4a4a" stroke-width="1" fill="none"/>
                                    </svg>
                                </div>
                                <h1 class="book-title">Omni</h1>
                                <h2 class="book-subtitle">International</h2>
                                <h3 class="book-journal">Journal</h3>
                                <h4 class="book-oij">(OIJ)</h4>
                            </div>
                        </div>

                        <!-- Spine -->
                        <div class="book-spine"></div>
                    </div>
                </div>
            </div>

            <div class="relative z-20 max-w-4xl px-4 sm:px-6 lg:px-8">
                <h1 class="text-3xl sm:text-5xl lg:text-6xl font-extrabold leading-tight mb-4 animate-fadeInDown">
                    Advancing Knowledge Across Disciplines
                </h1>
                <p class="text-lg sm:text-xl font-light mb-8 animate-fadeInUp">
                    A peer-reviewed journal publishing original research from the arts, sciences, humanities, and beyond.
                </p>
                <a href="#contact" class="bg-white text-black font-semibold py-3 px-8 rounded-full shadow-lg hover:bg-gray-200 transition-colors duration-300 transform hover:scale-105 inline-block">
                    Submit Your Manuscript
                </a>
            </div>
        </section>

        <!-- About Us Section -->
        <section id="about" class="py-16 sm:py-24 bg-black">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8">
                <div class="max-w-3xl mx-auto text-center">
                    <h2 class="text-3xl sm:text-4xl font-bold text-gray-100 mb-4">About Us</h2>
                    <div class="h-1 w-20 bg-white mx-auto rounded-full mb-8"></div>
                    <p class="text-xl text-gray-400 leading-relaxed mb-6 text-justify"></p>
                        <span class="font-bold text-gray-100">Omni International Journal (OIJ)</span> is a premier peer-reviewed, open-access journal dedicated to promoting excellence in scholarly research across a wide spectrum of disciplines. Our aim is to offer academicians, researchers, scholars, and students from diverse fields a dynamic forum for constructive deliberations on contemporary issues and groundbreaking research.
                    </p>
                    <p class="text-xl text-gray-400 leading-relaxed mb-6 text-justify"></p>
                        We strive to give our authors comprehensive support throughout the publishing journey, from the submission process to the post-publication phase. Our mission is to ensure that your research reaches the right audience and contributes meaningfully to global discourse. Our platform encourages engagement with academic disciplines that extend beyond traditional silos, providing a space for interdisciplinary studies and innovation. The journal provides a platform for a wide array of research areas, spanning from the humanities, social sciences, and business, to engineering, technology, and the natural sciences. We welcome submissions that explore new frontiers and bridge the gap between traditional fields.
                    </p>
                    <p class="text-xl text-gray-400 leading-relaxed mb-8 text-justify"></p>
                        To maintain the highest academic standards, we ensure a stringent double-blind peer review process. Our journal is indexed in reputable databases, serving as a bridge between theoretical research and practical application across all fields of study. OIJ is more than just a publication; it is a movement to advance knowledge and scholarship on a global scale. We are committed to fostering interdisciplinary dialogue and providing a platform for impactful research.
                    </p>
                    <div class="mt-8 text-center">
                        <span class="font-bold text-gray-100">Website:</span> <span class="text-white">[Your Website URL]</span><br>
                        <span class="font-bold text-gray-100">Email:</span> <span class="text-white">[Your Contact Email]</span><br>
                        <span class="font-bold text-gray-100">Follow Us:</span> <span class="text-white">[Social Media Handles]</span>
                    </div>
                </div>

                <div class="mt-16 text-center">
                    <span class="font-bold text-gray-100 block mb-6 text-3xl">Why Publish with Us?</span>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        <!-- Feature Box 1 -->
                        <div class="w-full p-4 rounded-xl shadow-lg flex flex-col items-center text-center transition-shadow duration-300 hover:shadow-[0_8px_32px_0_rgba(255,255,255,0.35)]" style="background: rgba(186, 186, 186, 0.352); border: 1px solid rgba(255, 255, 255, 0.2); backdrop-filter: blur(8px);">
                            <div class="publish-icon text-4xl mb-2 flex items-center justify-center rounded-full bg-black w-16 h-16 text-white">📖</div>
                            <h4 class="mt-2 text-xl font-bold text-white">Scholarly Excellence</h4>
                            <p class="mt-2 text-lg text-white">
                                High-quality research papers curated by a distinguished and diverse editorial board.
                            </p>
                        </div>
                        <!-- Feature Box 2 -->
                        <div class="w-full p-4 rounded-xl shadow-lg flex flex-col items-center text-center transition-shadow duration-300 hover:shadow-[0_8px_32px_0_rgba(255,255,255,0.35)]" style="background: rgba(186, 186, 186, 0.352); border: 1px solid rgba(255, 255, 255, 0.2); backdrop-filter: blur(8px);">
                            <div class="publish-icon text-4xl mb-2 flex items-center justify-center rounded-full bg-black w-16 h-16 text-white">🌍</div>
                            <h4 class="mt-2 text-xl font-bold text-gray-100">Global Reach</h4>
                            <p class="mt-2 text-lg text-white">
                                Your research gains maximum visibility through our extensive indexing in prominent academic databases.
                            </p>
                        </div>
                        <!-- Feature Box 3 -->
                        <div class="w-full p-4 rounded-xl shadow-lg flex flex-col items-center text-center transition-shadow duration-300 hover:shadow-[0_8px_32px_0_rgba(255,255,255,0.35)]" style="background: rgba(186, 186, 186, 0.352); border: 1px solid rgba(255, 255, 255, 0.2); backdrop-filter: blur(8px);">
                            <div class="publish-icon text-4xl mb-2 flex items-center justify-center rounded-full bg-black w-16 h-16 text-white">⚡</div>
                            <h4 class="mt-2 text-xl font-bold text-gray-100">Efficient Services</h4>
                            <p class="mt-2 text-lg text-white">
                                Fast-track peer review and a timely publication process to get your research to the world without delay.
                            </p>
                        </div>
                        <!-- Feature Box 4 -->
                        <div class="w-full p-4 rounded-xl shadow-lg flex flex-col items-center text-center transition-shadow duration-300 hover:shadow-[0_8px_32px_0_rgba(255,255,255,0.35)]" style="background: rgba(186, 186, 186, 0.352); border: 1px solid rgba(255, 255, 255, 0.2); backdrop-filter: blur(8px);">
                            <div class="publish-icon text-4xl mb-2 flex items-center justify-center rounded-full bg-black w-16 h-16 text-white">🏆</div>
                            <h4 class="mt-2 text-xl font-bold text-gray-100">Credibility & Recognition</h4>
                            <p class="mt-2 text-lg text-white">
                                Contributors receive a publication certificate and a unique DOI.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Disciplines Section -->
        <section id="disciplines" class="py-16 sm:py-24 bg-black">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center mb-12">
                    <h2 class="text-3xl sm:text-4xl font-bold text-gray-100 mb-4">Our Disciplines</h2>
                    <div class="h-1 w-20 bg-white mx-auto rounded-full"></div>
                </div>
                
                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
                    <!-- Discipline Card 1 -->
                    <div class="bg-black p-8 rounded-lg shadow-md transition-shadow duration-300 hover:shadow-[0_8px_32px_0_rgba(255,255,255,0.35)]" style="background: rgba(186, 186, 186, 0.352); border: 1px solid rgba(255, 255, 255, 0.2); backdrop-filter: blur(8px);">
                        <div class="text-white mb-4">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold mb-2 text-gray-100">Science & Technology</h3>
                        <p class="text-white">
                            Covering a wide range of topics, from computer science and engineering to biology and physics.
                        </p>
                    </div>

                    <!-- Discipline Card 2 -->
                    <div class="bg-black p-8 rounded-lg shadow-md transition-shadow duration-300 hover:shadow-[0_8px_32px_0_rgba(255,255,255,0.35)]" style="background: rgba(186, 186, 186, 0.352); border: 1px solid rgba(255, 255, 255, 0.2); backdrop-filter: blur(8px);">
                        <div class="text-white mb-4">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C9.555 5.56 7.425 6 6 6a3 3 0 00-3 3v2a2 2 0 002 2h4.5a3 3 0 013 3v2a2 2 0 01-2 2h-4.5a3 3 0 01-3-3v-2m15.5 0c1.455.56 3.585 0 5 0a3 3 0 003-3V9a2 2 0 00-2-2h-4.5a3 3 0 01-3-3v-2a2 2 0 012-2h4.5a3 3 0 013 3v2" />
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold mb-2 text-gray-100">Humanities & Arts</h3>
                        <p class="text-white">
                            Exploring literature, history, philosophy, visual arts, and cultural studies.
                        </p>
                    </div>

                    <!-- Discipline Card 3 -->
                    <div class="bg-black p-8 rounded-lg shadow-md transition-shadow duration-300 hover:shadow-[0_8px_32px_0_rgba(255,255,255,0.35)]" style="background: rgba(186, 186, 186, 0.352); border: 1px solid rgba(255, 255, 255, 0.2); backdrop-filter: blur(8px);">
                        <div class="text-white mb-4">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h2a2 2 0 002-2V7a2 2 0 00-2-2h-3v4M5 20h2a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v11a2 2 0 002 2zM12 5.5v11.5M12 5.5a2 2 0 01-2-2" />
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold mb-2 text-gray-100">Social Sciences</h3>
                        <p class="text-white">
                            Delving into sociology, economics, psychology, political science, and anthropology.
                        </p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Editorial Board Section -->
        <section id="editorial-board" class="py-16 sm:py-24 bg-black">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center mb-12">
                    <h2 class="text-3xl sm:text-4xl font-bold text-gray-100 mb-4">Meet Our Editorial Board</h2>
                    <div class="h-1 w-20 bg-white mx-auto rounded-full"></div>
                </div>

                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
                    <!-- Board Member 1 -->
                    <div class="p-6 rounded-lg shadow-md transition-shadow duration-300 hover:shadow-[0_8px_32px_0_rgba(255,255,255,0.35)] text-center" style="background: rgba(186, 186, 186, 0.352); border: 1px solid rgba(255, 255, 255, 0.2); backdrop-filter: blur(8px);">
                        <img src="https://placehold.co/150x150/1f2937/ffffff?text=Vishesh" alt="Adv. Vishesh Yadav" class="w-32 h-32 mx-auto rounded-full object-cover mb-4 shadow-md">
                        <h3 class="text-xl font-semibold text-gray-100">Adv. Vishesh Yadav</h3>
                        <p class="text-white font-medium">Editor in Chief</p>
                        <p class="text-gray-400 mt-2">Specializing in criminal law and IPR.</p>
                    </div>

                    <!-- Board Member 2 -->
                    <div class="p-6 rounded-lg shadow-md transition-shadow duration-300 hover:shadow-[0_8px_32px_0_rgba(255,255,255,0.35)] text-center" style="background: rgba(186, 186, 186, 0.352); border: 1px solid rgba(255, 255, 255, 0.2); backdrop-filter: blur(8px);">
                        <img src="https://placehold.co/150x150/1f2937/ffffff?text=John" alt="Dr. John Smith" class="w-32 h-32 mx-auto rounded-full object-cover mb-4 shadow-md">
                        <h3 class="text-xl font-semibold text-gray-100">Dr. John Smith</h3>
                        <p class="text-white font-medium">Associate Editor</p>
                        <p class="text-gray-400 mt-2">Expertise in ancient history and digital archives.</p>
                    </div>

                    <!-- Board Member 3 -->
                    <div class="p-6 rounded-lg shadow-md transition-shadow duration-300 hover:shadow-[0_8px_32px_0_rgba(255,255,255,0.35)] text-center" style="background: rgba(186, 186, 186, 0.352); border: 1px solid rgba(255, 255, 255, 0.2); backdrop-filter: blur(8px);">
                        <img src="https://placehold.co/150x150/1f2937/ffffff?text=Emily" alt="Dr. Emily White" class="w-32 h-32 mx-auto rounded-full object-cover mb-4 shadow-md">
                        <h3 class="text-xl font-semibold text-gray-100">Dr. Emily White</h3>
                        <p class="text-white font-medium">Reviewer Board</p>
                        <p class="text-gray-400 mt-2">Focus on sociology and urban development.</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Latest Publications Section -->
        <section id="publications" class="py-16 sm:py-24 bg-black">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center mb-8">
                    <h2 class="text-3xl sm:text-4xl font-bold text-gray-100 mb-4">Latest Publications</h2>
                    <div class="h-1 w-20 bg-white mx-auto rounded-full"></div>
                </div>

                <!-- Filter buttons -->
                <div class="flex flex-wrap justify-center space-x-2 sm:space-x-4 mb-12">
                    <button class="filter-btn active-filter-btn px-6 py-2 rounded-full text-sm font-semibold transition-colors duration-200" data-filter="all">All</button>
                    <button class="filter-btn px-6 py-2 rounded-full text-sm font-semibold transition-colors duration-200" data-filter="research-paper">Research Papers</button>
                    <button class="filter-btn px-6 py-2 rounded-full text-sm font-semibold transition-colors duration-200" data-filter="blog">Blogs</button>
                    <button class="filter-btn px-6 py-2 rounded-full text-sm font-semibold transition-colors duration-200" data-filter="article">Articles</button>
                </div>

                <div id="publications-grid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <!-- Publication Card 1 (Research Paper) -->
                    <div class="publication-card rounded-lg shadow-md transition-transform duration-300 hover:scale-105 overflow-hidden" data-category="research-paper" style="background: rgba(186, 186, 186, 0.35); border: 1px solid rgba(255, 255, 255, 0.2); backdrop-filter: blur(8px);">
                        <img src="https://placehold.co/600x400/2d3748/ffffff?text=Research+Paper" alt="Research paper placeholder image" class="w-full h-48 object-cover">
                        <div class="p-6">
                            <span class="text-xs font-semibold uppercase tracking-wide text-white">Research Paper</span>
                            <h3 class="text-xl font-semibold text-gray-100 mt-2 mb-2">The Future of AI Ethics in Healthcare</h3>
                            <p class="text-sm text-gray-400 mb-4">by A. B. Smith, et al. | Published: Oct 2024</p>
                            <p class="text-gray-400">
                                This paper examines the evolving ethical considerations in artificial intelligence development...
                            </p>
                            <a href="#" class="mt-4 inline-block text-white hover:underline font-semibold">Read More &rarr;</a>
                        </div>
                    </div>

                    <!-- Publication Card 2 (Article) -->
                    <div class="publication-card rounded-lg shadow-md transition-transform duration-300 hover:scale-105 overflow-hidden" data-category="article" style="background: rgba(186, 186, 186, 0.35); border: 1px solid rgba(255, 255, 255, 0.2); backdrop-filter: blur(8px);">
                        <img src="https://placehold.co/600x400/2d3748/ffffff?text=Article" alt="Article placeholder image" class="w-full h-48 object-cover">
                        <div class="p-6">
                            <span class="text-xs font-semibold uppercase tracking-wide text-white">Article</span>
                            <h3 class="text-xl font-semibold text-gray-100 mt-2 mb-2">Climate Change and Urban Resilience</h3>
                            <p class="text-sm text-gray-400 mb-4">by J. P. Williams, et al. | Published: Sep 2024</p>
                            <p class="text-gray-400">
                                Investigating urban planning strategies to mitigate the effects of climate change...
                            </p>
                            <a href="#" class="mt-4 inline-block text-white hover:underline font-semibold">Read More &rarr;</a>
                        </div>
                    </div>

                    <!-- Publication Card 3 (Blog) -->
                    <div class="publication-card rounded-lg shadow-md transition-transform duration-300 hover:scale-105 overflow-hidden" data-category="blog" style="background: rgba(186, 186, 186, 0.35); border: 1px solid rgba(255, 255, 255, 0.2); backdrop-filter: blur(8px);">
                        <img src="https://placehold.co/600x400/2d3748/ffffff?text=Blog+Post" alt="Blog post placeholder image" class="w-full h-48 object-cover">
                        <div class="p-6">
                            <span class="text-xs font-semibold uppercase tracking-wide text-white">Blog</span>
                            <h3 class="text-xl font-semibold text-gray-100 mt-2 mb-2">Why Interdisciplinary Research Matters</h3>
                            <p class="text-sm text-gray-400 mb-4">by L. K. Davis | Published: Aug 2024</p>
                            <p class="text-gray-400">
                                A look at how breaking down academic silos can lead to groundbreaking discoveries...
                            </p>
                            <a href="#" class="mt-4 inline-block text-white hover:underline font-semibold">Read More &rarr;</a>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Contact Form Section -->
        <section id="contact" class="py-16 sm:py-24 bg-black">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center mb-12">
                    <h2 class="text-3xl sm:text-4xl font-bold text-gray-100 mb-4">Contact Our Editorial Office</h2>
                    <div class="h-1 w-20 bg-white mx-auto rounded-full"></div>
                </div>

                <div class="max-w-xl mx-auto rounded-lg shadow-md" style="background: rgba(255, 255, 255, 0.1); border: 1px solid rgba(255, 255, 255, 0.2); backdrop-filter: blur(8px);">
                    <form action="#" method="POST" class="space-y-6 p-8">
                        <div>
                            <label for="name" class="block text-sm font-medium text-gray-400">Full Name</label>
                            <input type="text" id="name" name="name" required class="mt-1 block w-full px-4 py-3 border border-gray-700 bg-black text-gray-200 rounded-md shadow-sm focus:ring-gray-300 focus:border-gray-300 transition-colors duration-200">
                        </div>
                        <div>
                            <label for="email" class="block text-sm font-medium text-gray-400">Email Address</label>
                            <input type="email" id="email" name="email" required class="mt-1 block w-full px-4 py-3 border border-gray-700 bg-black text-gray-200 rounded-md shadow-sm focus:ring-gray-300 focus:border-gray-300 transition-colors duration-200">
                        </div>
                        <div>
                            <label for="message" class="block text-sm font-medium text-gray-400">Your Message</label>
                            <textarea id="message" name="message" rows="4" required class="mt-1 block w-full px-4 py-3 border border-gray-700 bg-black text-gray-200 rounded-md shadow-sm focus:ring-gray-300 focus:border-gray-300 transition-colors duration-200"></textarea>
                        </div>
                        <div>
                            <button type="submit" class="w-full bg-white text-black font-semibold py-3 px-6 rounded-md shadow-md hover:bg-gray-200 transition-colors duration-300 transform hover:scale-105">
                                Send Message
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="bg-black text-white py-12">
        <div class="container mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <!-- About Section -->
                <div>
                    <h4 class="text-lg font-bold mb-4 text-white">Global Journal</h4>
                    <p class="text-gray-400 text-sm">
                        Publishing high-impact research to advance knowledge across all disciplines.
                    </p>
                </div>
                <!-- Quick Links -->
                <div>
                    <h4 class="text-lg font-bold mb-4 text-white">Quick Links</h4>
                    <ul class="space-y-2">
                        <li><a href="#about" class="text-gray-400 hover:text-white transition-colors duration-200">About the Journal</a></li>
                        <li><a href="#disciplines" class="text-gray-400 hover:text-white transition-colors duration-200">Disciplines</a></li>
                        <li><a href="#editorial-board" class="text-gray-400 hover:text-white transition-colors duration-200">Editorial Board</a></li>
                        <li><a href="#publications" class="text-gray-400 hover:text-white transition-colors duration-200">Latest Publications</a></li>
                        <li><a href="#contact" class="text-gray-400 hover:text-white transition-colors duration-200">Contact</a></li>
                    </ul>
                </div>
                <!-- Contact Info -->
                <div>
                    <h4 class="text-lg font-bold mb-4 text-white">Editorial Office</h4>
                    <p class="text-gray-400 text-sm">
                        Vikalp Khand, Gomti Nagar<br>
                        Lucknow, UP 226010<br>
                        Phone: (+91)9140331271<br>
                        Email: <EMAIL>
                    </p>
                </div>
                <!-- Social Media Links -->
                <div>
                    <h4 class="text-lg font-bold mb-4 text-white">Follow Us</h4>
                    <div class="flex space-x-4">
                        <a href="#" class="text-gray-400 hover:text-white transition-colors duration-200">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24"><path d="M12 2.163c3.204 0 3.584.013 4.858.07c1.472.07 2.138.318 2.682.53a.5.5 0 01.373.373c.212.544.46 1.21.53 2.682.057 1.274.07 1.654.07 4.858s-.013 3.584-.07 4.858c-.07 1.472-.318 2.138-.53 2.682a.5.5 0 01-.373.373c-.544.212-1.21.46-2.682.53-.057 1.274-.07 1.654-.07 4.858s.013 3.584.07 4.858c.07 1.472.318 2.138-.53 2.682a.5.5 0 01-.373.373c-.544.212-1.21.46-2.682.53-.057 1.274-.07 1.654-.07 4.858z"/></svg>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white transition-colors duration-200">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24"><path d="M22.675 3.325c-.275.122-.575.225-.875.325a8.91 8.91 0 00-1.075-.125c-.325-.025-.675-.025-1.025-.025c-1.125 0-2.225.225-3.25.675c-.975.475-1.875 1.125-2.675 1.95s-1.425 1.8-1.85 2.825c-.4 1.05-.625 2.15-.675 3.325c.025.2.025.4.05.6a14.28 14.28 0 01-2.925-.375c-.825-.225-1.625-.525-2.4-.925c-.8-.4-1.525-.85-2.225-1.425c-.475-.375-.925-.75-1.35-1.15a.5.5 0 00-.7-.05c-.175.125-.3.3-.375.525-.075.225-.075.475-.025.75.125.625.325 1.225.6 1.8a10.42 10.42 0 001.375 2.225c.6.625 1.275 1.15 2.05 1.6c.75.475 1.575.825 2.45 1.1a15.2 15.2 0 001.875.425c.875.075 1.775.125 2.7.125a7.35 7.35 0 001.275-.075c.95-.125 1.875-.4 2.775-.825c.9-.425 1.75-.975 2.525-1.65c.775-.675 1.45-1.475 2.05-2.375s1.075-1.9 1.425-2.975c.35-1.075.525-2.225.525-3.425v-.05c-.025-.25-.025-.5-.075-.725a.5.5 0 00-.3-.425c-.2-.075-.4-.1-.65-.1-.275 0-.5.05-.725.125-.2.075-.425.175-.625.3-.2.125-.375.25-.525.4a.5.5 0 00-.125.725c.075.2.1.4.075.6a.5.5 0 00.225.475c.15.1.325.15.525.15h.05c-.1.2-.2.4-.3.6a.5.5 0 00-.3.425c-.1.2-.125.425-.075.675z"/></svg>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white transition-colors duration-200">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24"><path d="M12 2.163c3.204 0 3.584.013 4.858.07c1.472.07 2.138.318 2.682.53a.5.5 0 01.373.373c.212.544.46 1.21.53 2.682.057 1.274.07 1.654.07 4.858s-.013 3.584-.07 4.858c-.07 1.472-.318 2.138-.53 2.682a.5.5 0 01-.373.373c-.544.212-1.21.46-2.682.53-.057 1.274-.07 1.654-.07 4.858z"/></svg>
                        </a>
                    </div>
                </div>
            </div>
            <div class="mt-8 pt-8 border-t border-gray-700 text-center">
                <p class="text-sm text-gray-400">&copy; 2025 Global Journal. All Rights Reserved.</p>
            </div>
        </div>
    </footer>

    <script>
        // JavaScript for mobile menu functionality
        const mobileMenuBtn = document.getElementById('mobile-menu-btn');
        const closeMobileMenuBtn = document.getElementById('close-mobile-menu-btn');
        const mobileMenu = document.getElementById('mobile-menu');
        const mobileLinks = mobileMenu.querySelectorAll('a');

        mobileMenuBtn.addEventListener('click', () => {
            mobileMenu.classList.add('open');
            document.body.style.overflow = 'hidden'; // Prevents scrolling behind the menu
        });

        closeMobileMenuBtn.addEventListener('click', () => {
            mobileMenu.classList.remove('open');
            document.body.style.overflow = 'auto';
        });

        // Close mobile menu when a link is clicked
        mobileLinks.forEach(link => {
            link.addEventListener('click', () => {
                mobileMenu.classList.remove('open');
                document.body.style.overflow = 'auto';
            });
        });

        // JavaScript for scroll-to-top button
        const scrollToTopBtn = document.getElementById('scrollToTopBtn');

        window.onscroll = function() {
            if (document.body.scrollTop > 300 || document.documentElement.scrollTop > 300) {
                scrollToTopBtn.style.display = "block";
            } else {
                scrollToTopBtn.style.display = "none";
            }
        };

        scrollToTopBtn.addEventListener('click', () => {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });

        // JavaScript for publication filtering
        const filterButtons = document.querySelectorAll('.filter-btn');
        const publicationsGrid = document.getElementById('publications-grid');
        const publicationCards = document.querySelectorAll('.publication-card');

        filterButtons.forEach(button => {
            button.addEventListener('click', () => {
                // Remove 'active' class from all buttons and their active styles
                filterButtons.forEach(btn => {
                    btn.classList.remove('active-filter-btn', 'bg-white', 'text-black');
                    btn.classList.add('bg-gray-900', 'text-gray-400', 'hover:bg-gray-800', 'hover:text-gray-300');
                });
                
                // Add 'active' class to the clicked button and remove inactive styles
                button.classList.add('active-filter-btn');
                button.classList.remove('bg-gray-900', 'text-gray-400', 'hover:bg-gray-800', 'hover:text-gray-300');
                button.classList.add('bg-white', 'text-black');

                const filter = button.dataset.filter;

                // Loop through all publication cards and show/hide based on the filter
                publicationCards.forEach(card => {
                    const category = card.dataset.category;
                    if (filter === 'all' || category === filter) {
                        card.style.display = 'block';
                    } else {
                        card.style.display = 'none';
                    }
                });
            });
        });

        // JavaScript for custom cursor
        const customCursor = document.getElementById('custom-cursor');
        document.addEventListener('mousemove', (e) => {
            customCursor.style.left = e.clientX + 'px';
            customCursor.style.top = e.clientY + 'px';
        });

        // ===== Enhanced 3D Book Interactions =====
        const hero = document.getElementById('hero');
        const book = document.querySelector('.book');
        const cover = document.querySelector('.book-cover');
        const pages = Array.from(document.querySelectorAll('.page'));

        // Safety: bail if elements not found
        if (hero && book && cover && pages.length) {
            let targetAngle = 0; // degrees; 0 = closed, 60 = open
            let currentAngle = 0;
            let rafId = null;

            // Map mouse X across hero into open angle
            function handlePointerMove(e) {
                const rect = hero.getBoundingClientRect();
                const x = Math.min(Math.max(e.clientX - rect.left, 0), rect.width);
                const ratio = x / rect.width; // 0..1
                targetAngle = 12 + ratio * 48; // 12deg min ajar to 60deg max
            }

            function animate() {
                // ease towards target
                currentAngle += (targetAngle - currentAngle) * 0.08;
                // Apply to cover and pages progressively
                cover.style.setProperty('--cover-angle', `${-currentAngle}deg`);
                pages.forEach((pg, i) => {
                    const step = (i + 1) / pages.length; // 0..1
                    const pgAngle = -Math.max(2 + step * currentAngle, 2 + i * 2);
                    pg.style.transform = `rotateY(${pgAngle}deg) translateZ(${i + 1}px)`;
                });
                // Subtle scene tilt based on angle
                const tiltY = -18 + currentAngle * 0.05;
                const tiltX = 8 + Math.sin(currentAngle * Math.PI / 180) * 1.2;
                const tiltZ = -2 + Math.cos(currentAngle * Math.PI / 180) * 0.8;
                // Keep matte, solid — no reflective glows
                book.style.transform = `rotateY(${tiltY}deg) rotateX(${tiltX}deg) rotateZ(${tiltZ}deg)`;
                rafId = requestAnimationFrame(animate);
            }

            // Start animation loop unless reduced motion
            const mediaReduce = window.matchMedia('(prefers-reduced-motion: reduce)');
            if (!mediaReduce.matches) {
                rafId = requestAnimationFrame(animate);
                hero.addEventListener('mousemove', handlePointerMove);
                hero.addEventListener('touchmove', (e) => {
                    if (e.touches && e.touches[0]) {
                        handlePointerMove(e.touches[0]);
                    }
                }, { passive: true });
                // Reset angle when leaving hero
                hero.addEventListener('mouseleave', () => { targetAngle = 18; });
            }

            // Accessibility: keep book slightly open on focus within hero (keyboard users)
            hero.addEventListener('focusin', () => { targetAngle = 24; });
        }
    </script>
</body>
</html>
