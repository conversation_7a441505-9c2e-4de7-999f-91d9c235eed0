<!DOCTYPE html>
<html lang="en" class="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Journal Website - Dark Mode Toggle</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        // Configure Tailwind to use class-based dark mode
        tailwind.config = {
            darkMode: 'class',
        }
    </script>
    <style>
        /* Custom styles for smooth transitions */
        * {
            transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
        }
        
        /* Toggle button styles */
        .toggle-button {
            position: relative;
            width: 60px;
            height: 30px;
            background-color: #e5e7eb;
            border-radius: 15px;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }
        
        .toggle-button.dark {
            background-color: #374151;
        }
        
        .toggle-slider {
            position: absolute;
            top: 3px;
            left: 3px;
            width: 24px;
            height: 24px;
            background-color: white;
            border-radius: 50%;
            transition: transform 0.3s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }
        
        .toggle-button.dark .toggle-slider {
            transform: translateX(30px);
        }
        
        /* Feature box hover effects */
        .feature-box {
            transition: all 0.3s ease;
        }
        
        .feature-box:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }
        
        .dark .feature-box:hover {
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
        }
    </style>
</head>
<body class="bg-white dark:bg-gray-900 text-gray-900 dark:text-white">
    <!-- Header with Dark Mode Toggle -->
    <header class="bg-white dark:bg-gray-800 shadow-md dark:shadow-gray-700/50 sticky top-0 z-50">
        <div class="container mx-auto px-6 py-4">
            <div class="flex justify-between items-center">
                <h1 class="text-2xl font-bold text-blue-600 dark:text-blue-400">Journal Website</h1>
                
                <!-- Dark Mode Toggle Button -->
                <div class="flex items-center space-x-3">
                    <span class="text-sm text-gray-600 dark:text-gray-300">☀️</span>
                    <div class="toggle-button" id="darkModeToggle">
                        <div class="toggle-slider"></div>
                    </div>
                    <span class="text-sm text-gray-600 dark:text-gray-300">🌙</span>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto px-6 py-8">
        <!-- Hero Section -->
        <section class="text-center mb-16">
            <h2 class="text-4xl font-bold mb-4 text-gray-800 dark:text-white">Welcome to Our Journal</h2>
            <p class="text-xl text-gray-600 dark:text-gray-300">Discover amazing content and insights</p>
        </section>

        <!-- Feature Boxes Section -->
        <section class="mb-16">
            <h3 class="text-3xl font-bold text-center mb-12 text-gray-800 dark:text-white">Features</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Feature Box 1 -->
                <div class="feature-box bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700">
                    <div class="text-blue-500 dark:text-blue-400 text-4xl mb-4">📚</div>
                    <h4 class="text-xl font-semibold mb-3 text-gray-800 dark:text-white">Research Articles</h4>
                    <p class="text-gray-600 dark:text-gray-300">Access to peer-reviewed research articles and academic papers.</p>
                </div>

                <!-- Feature Box 2 -->
                <div class="feature-box bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700">
                    <div class="text-green-500 dark:text-green-400 text-4xl mb-4">🔬</div>
                    <h4 class="text-xl font-semibold mb-3 text-gray-800 dark:text-white">Scientific Studies</h4>
                    <p class="text-gray-600 dark:text-gray-300">Latest scientific studies and experimental findings.</p>
                </div>

                <!-- Feature Box 3 -->
                <div class="feature-box bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700">
                    <div class="text-purple-500 dark:text-purple-400 text-4xl mb-4">📊</div>
                    <h4 class="text-xl font-semibold mb-3 text-gray-800 dark:text-white">Data Analysis</h4>
                    <p class="text-gray-600 dark:text-gray-300">Comprehensive data analysis and statistical insights.</p>
                </div>

                <!-- Feature Box 4 -->
                <div class="feature-box bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700">
                    <div class="text-red-500 dark:text-red-400 text-4xl mb-4">🎯</div>
                    <h4 class="text-xl font-semibold mb-3 text-gray-800 dark:text-white">Expert Reviews</h4>
                    <p class="text-gray-600 dark:text-gray-300">Expert reviews and professional opinions on various topics.</p>
                </div>

                <!-- Feature Box 5 -->
                <div class="feature-box bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700">
                    <div class="text-yellow-500 dark:text-yellow-400 text-4xl mb-4">💡</div>
                    <h4 class="text-xl font-semibold mb-3 text-gray-800 dark:text-white">Innovation Hub</h4>
                    <p class="text-gray-600 dark:text-gray-300">Cutting-edge innovations and breakthrough technologies.</p>
                </div>

                <!-- Feature Box 6 -->
                <div class="feature-box bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700">
                    <div class="text-indigo-500 dark:text-indigo-400 text-4xl mb-4">🌐</div>
                    <h4 class="text-xl font-semibold mb-3 text-gray-800 dark:text-white">Global Network</h4>
                    <p class="text-gray-600 dark:text-gray-300">Connect with researchers and professionals worldwide.</p>
                </div>
            </div>
        </section>

        <!-- Disciplines Section (Example with existing hover effects) -->
        <section class="mb-16">
            <h3 class="text-3xl font-bold text-center mb-12 text-gray-800 dark:text-white">Disciplines</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <!-- Discipline Box 1 -->
                <div class="feature-box bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900 dark:to-blue-800 p-6 rounded-lg border border-blue-200 dark:border-blue-700">
                    <div class="text-blue-600 dark:text-blue-300 text-3xl mb-3">🧬</div>
                    <h4 class="text-lg font-semibold text-blue-800 dark:text-blue-200">Biology</h4>
                </div>

                <!-- Discipline Box 2 -->
                <div class="feature-box bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900 dark:to-green-800 p-6 rounded-lg border border-green-200 dark:border-green-700">
                    <div class="text-green-600 dark:text-green-300 text-3xl mb-3">⚗️</div>
                    <h4 class="text-lg font-semibold text-green-800 dark:text-green-200">Chemistry</h4>
                </div>

                <!-- Discipline Box 3 -->
                <div class="feature-box bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900 dark:to-purple-800 p-6 rounded-lg border border-purple-200 dark:border-purple-700">
                    <div class="text-purple-600 dark:text-purple-300 text-3xl mb-3">🔬</div>
                    <h4 class="text-lg font-semibold text-purple-800 dark:text-purple-200">Physics</h4>
                </div>

                <!-- Discipline Box 4 -->
                <div class="feature-box bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-900 dark:to-orange-800 p-6 rounded-lg border border-orange-200 dark:border-orange-700">
                    <div class="text-orange-600 dark:text-orange-300 text-3xl mb-3">💻</div>
                    <h4 class="text-lg font-semibold text-orange-800 dark:text-orange-200">Technology</h4>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-100 dark:bg-gray-800 py-8 mt-16">
        <div class="container mx-auto px-6 text-center">
            <p class="text-gray-600 dark:text-gray-300">&copy; 2024 Journal Website. All rights reserved.</p>
        </div>
    </footer>

    <!-- Dark Mode Toggle JavaScript -->
    <script>
        // Dark mode toggle functionality
        const darkModeToggle = document.getElementById('darkModeToggle');
        const htmlElement = document.documentElement;

        // Check for saved theme preference or default to light mode
        const currentTheme = localStorage.getItem('theme') || 'light';
        
        // Apply the current theme
        if (currentTheme === 'dark') {
            htmlElement.classList.add('dark');
            htmlElement.classList.remove('light');
            darkModeToggle.classList.add('dark');
        } else {
            htmlElement.classList.add('light');
            htmlElement.classList.remove('dark');
            darkModeToggle.classList.remove('dark');
        }

        // Toggle function
        function toggleDarkMode() {
            if (htmlElement.classList.contains('dark')) {
                // Switch to light mode
                htmlElement.classList.remove('dark');
                htmlElement.classList.add('light');
                darkModeToggle.classList.remove('dark');
                localStorage.setItem('theme', 'light');
            } else {
                // Switch to dark mode
                htmlElement.classList.remove('light');
                htmlElement.classList.add('dark');
                darkModeToggle.classList.add('dark');
                localStorage.setItem('theme', 'dark');
            }
        }

        // Add click event listener
        darkModeToggle.addEventListener('click', toggleDarkMode);

        // Optional: Listen for system theme changes
        window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
            if (!localStorage.getItem('theme')) {
                if (e.matches) {
                    htmlElement.classList.add('dark');
                    htmlElement.classList.remove('light');
                    darkModeToggle.classList.add('dark');
                } else {
                    htmlElement.classList.remove('dark');
                    htmlElement.classList.add('light');
                    darkModeToggle.classList.remove('dark');
                }
            }
        });
    </script>
</body>
</html>
